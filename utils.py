import logging
import requests
from groq import <PERSON>roq
from serpapi.google_search import GoogleSearch
from dotenv import load_dotenv
import os
import json
import re
import base64
from pathlib import Path

load_dotenv()

api_key = os.environ.get("GROQ_API_KEY")

client = Groq(api_key=api_key)
groq_model = "llama-3.3-70b-versatile"


def extract_json_from_text(text):
    """
    Advanced function to extract JSON arrays from text with better handling for nested structures.

    Args:
        text (str): Text containing JSON

    Returns:
        str: Extracted and validated JSON string
    """
    import re
    import json

    # First try to find JSO<PERSON> in code blocks
    match = re.search(r'```(?:json)?\s*(\[[\s\S]*?\])\s*```', text, re.DOTALL)
    if match:
        try:
            # Validate it's proper JSON by parsing and re-stringifying
            json_obj = json.loads(match.group(1))
            return json.dumps(json_obj)
        except json.JSONDecodeError:
            pass  # If invalid, continue to other methods

    # Look for array pattern with better handling for nested structures
    text = text.strip()
    if text.startswith('[') and text.endswith(']'):
        try:
            # Validate it's proper JSON
            json_obj = json.loads(text)
            return json.dumps(json_obj)
        except json.JSONDecodeError:
            pass  # Continue to more aggressive extraction

    # More aggressive approach: find the outermost array
    # Count opening and closing brackets to handle nesting properly
    start_idx = text.find('[')
    if start_idx >= 0:
        bracket_count = 0
        end_idx = -1

        for i in range(start_idx, len(text)):
            if text[i] == '[':
                bracket_count += 1
            elif text[i] == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    end_idx = i
                    break

        if end_idx > start_idx:
            potential_json = text[start_idx:end_idx + 1]
            try:
                # Validate it's proper JSON
                json_obj = json.loads(potential_json)
                return json.dumps(json_obj)
            except json.JSONDecodeError:
                pass  # Continue to other methods

    # Last resort: try to find any valid JSON array in the text
    # Use a simple regex to find potential start and end points
    array_starts = [m.start() for m in re.finditer(r'\[', text)]
    for start in array_starts:
        bracket_count = 0
        end_idx = -1

        for i in range(start, len(text)):
            if text[i] == '[':
                bracket_count += 1
            elif text[i] == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    end_idx = i
                    break

        if end_idx > start:
            potential_json = text[start:end_idx + 1]
            try:
                # Validate it's proper JSON
                json_obj = json.loads(potential_json)
                return json.dumps(json_obj)
            except json.JSONDecodeError:
                continue  # Try next potential start position

    # If we get here, we couldn't extract valid JSON
    raise ValueError("Could not extract valid JSON array from the provided text")

def img_to_base64(img_path):
    try:
        img_path = Path(img_path)
        if not img_path.exists():
            return "", False
        with open(img_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode("utf-8"), True
    except Exception as e:
        print(f"Error loading image: {e}")
        return "", False

def get_patent_full_details(patent_number):
    """
    Fetch comprehensive details for a given patent number, including assignees,
    independent claims, priority date, title, abstract, and forward citation assignees.
    """
    details = {
        "assignees": [],
        "independent_claims": [],
        "priority_date": None,
        "title": None,
        "abstract": None,
        "forward_citation_assignees": [],
        "error": None
    }
    try:

        url = f"https://api.patent.wissenresearch.com/patent/{patent_number}"
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()

        details["assignees"] = data.get('assignees', [])
        details["independent_claims"] = data.get('independent_claims', [])
        details["priority_date"] = data.get('priority_date')
        details["title"] = data.get('title')
        details["abstract"] = data.get('abstract')
        details["forward_citation_assignees"] = data.get('forward_citation_assignees', [])

        # Log if crucial parts are missing
        if not details["independent_claims"]:
            logging.warning(f"No independent claims found for {patent_number} via API.")
        if not details["priority_date"]:
            logging.warning(f"No priority date found for {patent_number} via API.")

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching patent data for {patent_number} from API: {e}")
        details["error"] = str(e)
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON response for {patent_number} from API: {e}")
        details["error"] = "Invalid JSON response from patent API."
    return details


class LitigationSearch:
    def __init__(self, serpapi_key):
        self.serpapi_key = serpapi_key
        self.non_companies = [
            "et al", "and others", "et. al.", "et. al", "etc", "and others",
            "and affiliates", "all", "the", "united states", "district court",
            "patent", "litigation", "inc", "llc", "corporation", "co", "ltd"
        ]

    def is_valid_company(self, name):
        if not name or len(name) < 3:
            return False
        name_lower = name.lower()
        if any(term == name_lower for term in self.non_companies):
            return False
        if any(term in name_lower and len(name_lower) < len(term) + 3 for term in self.non_companies):
            return False
        if name_lower.startswith("v.") or name_lower == "v":
            return False
        if re.match(r'^[0-9\W]+$', name):
            return False
        return True

    @staticmethod
    def extract_competitor_from_serp_api_results(assignee, titles):
        if not client:
            logging.error("Groq client not initialized in LitigationSearch. Cannot extract competitors.")
            return []
        try:
            titles_json = json.dumps(titles)
            prompt = f"""
            Extract all company names that {assignee} has filed patent infringement lawsuits against or that have filed lawsuits against {assignee}, based on these lawsuit titles.

            IMPORTANT: 
            1. Do NOT include {assignee} in the results.
            2. Only return a JSON array of company names, nothing else.
            3. Only extract actual company names that appear in the "v." or "vs." or "versus" format of case titles.
            4. Do not include "et al", "and others", or other legal phrases.
            5. Format each company name properly (e.g., "Samsung Electronics Co., Ltd.") 
            6. Only return Company names in the output, nothing else. Do not provide note or any other text.

            Here are the lawsuit titles: {titles_json}
            """
            response = client.chat.completions.create(
                model=groq_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            result_text = response.choices[0].message.content.strip()
            companies = re.findall(r'["\']([^"\']+)["\']', result_text)
            return companies
        except Exception as e:
            logging.error(f"Error extracting competitors using Groq: {e}")
            return []

    def search_litigation_by_assignee(self, assignee, patent_assignees):
        search_query = f"{assignee} patent litigation"
        params = {
            "q": search_query,
            "hl": "en",
            "gl": "us",
            "api_key": self.serpapi_key
        }
        try:
            response = GoogleSearch(params).get_dict()
            titles = [result['title'] for result in response.get('organic_results', [])]
            # Pass current patent's assignees to avoid self-listing if assignee is part of a group name in litigation
            competitors = self.extract_competitor_from_serp_api_results(assignee, titles)
            return competitors
        except Exception as e:
            logging.error(f"Error searching litigation with SerpAPI: {e}")
            return []

    def get_competitors_from_patent(self, patent_number, patent_assignees_list):
        """Finds competitors for all assignees of a patent."""
        all_competitors = set()
        for assignee in patent_assignees_list:
            competitors = self.search_litigation_by_assignee(assignee, patent_assignees_list)
            all_competitors.update(competitors)

        filtered_competitors = [comp for comp in all_competitors if self.is_valid_company(comp)]
        return filtered_competitors
