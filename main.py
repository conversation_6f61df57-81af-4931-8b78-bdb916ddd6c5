import logging
import json
import os
import re
from datetime import datetime
from io import Bytes<PERSON>
from typing import List, Dict, Any, Optional

import pandas as pd
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from agno.agent import Agent
from agno.models.google import Gemini
from fastapi.middleware.cors import CORSMiddleware

from utils import get_patent_full_details, LitigationSearch, img_to_base64

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)

load_dotenv()

SERPAPI_API_KEY = os.environ.get("SERPAPI_API_KEY")
GROQ_API_KEY = os.environ.get("GROQ_API_KEY")
MODEL_ID = os.environ.get("MODEL_ID")

app = FastAPI(
    title="Patent Infringement Analysis API",
    description="API for analyzing patents, finding potentially infringing products, and creating claim charts",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Initialize model
try:
    model = Gemini(id=MODEL_ID, search=True, temperature=0.3)
    logging.info("Gemini model initialized successfully.")
except Exception as e:
    logging.critical(f"Failed to initialize Gemini model: {e}", exc_info=True)
    raise HTTPException(status_code=500, detail=f"Failed to initialize Gemini model: {str(e)}")

# Initialize LitigationSearch once
if SERPAPI_API_KEY:
    litigation_search_tool = LitigationSearch(serpapi_key=SERPAPI_API_KEY)
else:
    litigation_search_tool = None
    logging.warning("LitigationSearch tool not initialized due to missing SERPAPI_API_KEY.")

# Cache for patent details
patent_cache = {}


# Models for API requests and responses
class PatentRequest(BaseModel):
    patent_number: str = Field(..., example="US9253445B2", description="Patent number to analyze")


class NoveltyResponse(BaseModel):
    patent_number: str
    novelty_summary: str
    priority_date: Optional[str] = None
    competitors: List[str] = []
    assignees: List[str] = []


class ProductsResponse(BaseModel):
    patent_number: str
    products: List[Dict[str, Any]] = []


class ClaimChartResponse(BaseModel):
    patent_number: str
    claim_charts: List[Dict[str, Any]] = []


class FullAnalysisResponse(BaseModel):
    patent_number: str
    novelty_summary: str
    priority_date: Optional[str] = None
    competitors: List[str] = []
    assignees: List[str] = []
    products: List[Dict[str, Any]] = []
    claim_charts: List[Dict[str, Any]] = []


def cached_get_patent_full_details(patent_number):
    """Cache wrapper for get_patent_full_details"""
    if patent_number not in patent_cache:
        patent_cache[patent_number] = get_patent_full_details(patent_number)
    return patent_cache[patent_number]


def create_excel_analysis(phase1_data, phase2_data):
    """Creates an Excel file buffer containing Phase 1 and Phase 2 analysis."""
    output_buffer = BytesIO()
    with pd.ExcelWriter(output_buffer, engine='openpyxl') as writer:
        if phase1_data:
            df_phase1_data = []
            for item in phase1_data:
                links = item.get('infringement_evidence_links', [])
                df_phase1_data.append({
                    'company': item.get('company'),
                    'model': item.get('model'),
                    'launch_date': item.get('launch_date'),
                    'infringement_evidence_links': "\n".join(links) if isinstance(links, list) else links
                })

            if df_phase1_data:
                pd.DataFrame(df_phase1_data).to_excel(writer, sheet_name='Identified Products', index=False)
            else:
                pd.DataFrame({"Status": ["No products identified in Phase 1"]}).to_excel(writer,
                                                                                         sheet_name='Identified Products',
                                                                                         index=False)

        if phase2_data:
            # Similar to the original function, process claim charts
            # Shortened for brevity - full implementation would include all the chart processing logic
            pd.DataFrame({"Status": ["Claim chart data available"]}).to_excel(
                writer, sheet_name='Claim Chart Status', index=False)

            # Create a separate sheet for each product's claim chart
            for i, product_analysis in enumerate(phase2_data):
                company = product_analysis.get('company', 'Unknown Company')
                model = product_analysis.get('model', 'Unknown Model')
                sheet_name = f"{company}-{model}"[:31]  # Excel has a 31 character limit for sheet names

                # Handle duplicate sheet names
                if sheet_name in [ws.title for ws in writer.book.worksheets]:
                    sheet_name = f"{sheet_name}_{i}"[:31]

                # Create a DataFrame for this product's claim chart data
                claim_data = []
                for claim_element in product_analysis.get('claim_chart', []):
                    claim_data.append({
                        'Claim Element': claim_element.get('claim_element', ''),
                        'Product Element': claim_element.get('product_element', ''),
                        'Match Explanation': claim_element.get('match_explanation', '')
                    })

                if claim_data:
                    pd.DataFrame(claim_data).to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            pd.DataFrame({"Status": ["No claim chart data available"]}).to_excel(
                writer, sheet_name='Claim Chart Status', index=False)

    output_buffer.seek(0)
    return output_buffer


def extract_json_from_text(text):
    """Helper function to extract JSON from text that might contain markdown or other content."""
    # First try to extract JSON between markdown delimiters
    json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
    if json_match:
        json_str = json_match.group(1).strip()
    else:
        # If no markdown delimiters, try to find something that looks like a JSON array or object
        json_match = re.search(r'(\[[\s\S]*\]|\{[\s\S]*\})', text)
        if json_match:
            json_str = json_match.group(1).strip()
        else:
            # If all else fails, use the entire text
            json_str = text.strip()

    # Remove any trailing or leading text that's not part of the JSON
    if json_str.startswith('[') and ']' in json_str:
        end_index = json_str.rindex(']') + 1
        json_str = json_str[:end_index]
    elif json_str.startswith('{') and '}' in json_str:
        end_index = json_str.rindex('}') + 1
        json_str = json_str[:end_index]

    return json_str


def init_agents():
    """Initializes and returns all agents."""
    novelty_agent_instructions = """
            **Objective:** Analyze the provided patent information (title, abstract, and independent claims) to identify and summarize its core novelty and distinguishing technical features.
            # ... rest of instructions ...
            """
    novelty_analysis_agent = Agent(
        model=model,
        markdown=False,
        description="Expert in analyzing patent documents to extract and summarize core novelty.",
        instructions=[novelty_agent_instructions],
        show_tool_calls=True,
    )

    product_finder_agent_instructions = """
            **Objective:** Identify commercial products that may implement the novel features of the patent.
            # ... rest of instructions ...
            """
    product_finder_agent = Agent(
        model=model,
        markdown=True,
        description="Expert in finding commercial products relevant to patent novelty using official sources.",
        instructions=[product_finder_agent_instructions],
        show_tool_calls=True,
    )

    claim_chart_agent_instructions = """
            **Objective:** For each product identified in Phase 1, conduct a detailed analysis against the patent's independent claims to create a comprehensive claim chart and assess infringement risk.

            **Important Response Format:**
            Always return a valid JSON array. Each item in the array should be a JSON object with the following structure:
            ```json
            [
              {
                "company": "Company Name",
                "model": "Product Model",
                "claim_chart": [
                  {
                    "claim_element": "Text of claim element",
                    "product_element": "Product feature that may infringe",
                    "match_explanation": "Explanation of the match"
                  }
                ],
                "infringement_risk_level": "High/Medium/Low",
                "infringement_risk_explanation": "Explanation"
              }
            ]
            ```

            Your response must be valid JSON that starts with [ and ends with ] - no additional text.
            """
    claim_chart_agent = Agent(
        model=model,
        markdown=True,
        description="Expert in creating detailed patent claim charts by analyzing products against claims.",
        instructions=[claim_chart_agent_instructions],
        show_tool_calls=True,
    )

    logging.info("All agents initialized with updated instructions.")
    return novelty_analysis_agent, product_finder_agent, claim_chart_agent


@app.post("/novelty", response_model=NoveltyResponse,
          summary="Patent Novelty Analysis",
          description="Analyzes the novelty of a patent based on its independent claims")
async def analyze_novelty(request: PatentRequest):
    """Analyze patent novelty (Phase 0)"""
    patent_number = request.patent_number

    # Fetch patent details
    patent_data = cached_get_patent_full_details(patent_number)
    if patent_data.get("error"):
        raise HTTPException(status_code=404, detail=f"Failed to fetch patent details: {patent_data['error']}")

    # Extract necessary data
    patent_title = patent_data.get("title", "")
    patent_abstract = patent_data.get("abstract", "")
    independent_claims = patent_data.get("independent_claims", [])
    priority_date = patent_data.get("priority_date", "")
    assignees = patent_data.get("assignees", [])

    # Get competitors if litigation search tool is available
    competitors = []
    if litigation_search_tool and assignees:
        try:
            competitors = litigation_search_tool.get_competitors_from_patent(patent_number, assignees)
            logging.info(f"Found {len(competitors)} competitors for {patent_number}")
        except Exception as e:
            logging.error(f"Error finding competitors for {patent_number}: {e}", exc_info=True)

    # Initialize novelty agent
    novelty_analysis_agent, _, _ = init_agents()

    # Analyze novelty
    novelty_summary = "No novelty analysis available"
    if independent_claims:
        try:
            novelty_input_payload = {
                "patent_number": patent_number,
                "patent_title": patent_title if patent_title else "Not Available",
                "patent_abstract": patent_abstract if patent_abstract else "Not Available",
                "independent_claims": independent_claims
            }

            novelty_query = (
                f"Please analyze the following patent to determine its core novelty.\n"
                f"Patent Number: {novelty_input_payload['patent_number']}\n"
                f"Title: {novelty_input_payload['patent_title']}\n"
                f"Abstract: {novelty_input_payload['patent_abstract']}\n"
                f"Independent Claims:\n```json\n{json.dumps(novelty_input_payload['independent_claims'], indent=2)}\n```\n"
                f"Based on your instructions, provide ONLY the plain text novelty summary."
            )

            novelty_run = novelty_analysis_agent.run(novelty_query)

            if novelty_run and novelty_run.content:
                novelty_summary = novelty_run.content.strip()
                if not novelty_summary:
                    novelty_summary = "Novelty agent returned an empty summary."
                    logging.warning(f"Novelty extraction returned empty summary for {patent_number}")
            else:
                novelty_summary = "Novelty agent returned no content."
                logging.warning(f"Novelty extraction returned no content for {patent_number}")
        except Exception as e:
            novelty_summary = f"Error during novelty extraction: {str(e)[:100]}..."
            logging.error(f"Error during novelty extraction for {patent_number}: {e}", exc_info=True)
    else:
        novelty_summary = "No independent claims available to analyze for novelty."
        logging.warning(f"Skipping novelty analysis for {patent_number}: No independent claims.")

    return NoveltyResponse(
        patent_number=patent_number,
        novelty_summary=novelty_summary,
        priority_date=priority_date,
        competitors=competitors,
        assignees=assignees
    )


@app.post("/products", response_model=ProductsResponse,
          summary="Find Potentially Infringing Products",
          description="Identifies commercial products that may implement the novel features of the patent")
async def find_products(request: PatentRequest, novelty_data: Optional[NoveltyResponse] = None):
    """Find potentially infringing products (Phase 1)"""
    patent_number = request.patent_number

    # If novelty data wasn't provided, get it first
    if not novelty_data:
        novelty_data = await analyze_novelty(request)

    # Get patent data
    patent_data = cached_get_patent_full_details(patent_number)
    if patent_data.get("error"):
        raise HTTPException(status_code=404, detail=f"Failed to fetch patent details: {patent_data['error']}")

    independent_claims = patent_data.get("independent_claims", [])
    claim_for_product_finding = independent_claims[0] if independent_claims else ""

    if not claim_for_product_finding:
        raise HTTPException(status_code=400, detail="No independent claim text available for product finding")

    # Initialize product finder agent
    _, product_finder_agent, _ = init_agents()

    # Find products
    phase1_results = []
    try:
        finder_input_payload = {
            "Patent Novelty Summary": novelty_data.novelty_summary,
            "Claim Text": claim_for_product_finding,
            "Priority Date": novelty_data.priority_date,
            "Known Competitors": novelty_data.competitors,
            "Assignee Companies": novelty_data.assignees
        }

        finder_query = (
            f"Based on the following details, execute your instructions and return ONLY the specified JSON list:\n\n"
            f"**Patent Novelty Summary (Use this as your PRIMARY GUIDE for identifying relevant products):**\n{finder_input_payload['Patent Novelty Summary']}\n\n"
            f"**Claim Text (Refer to this for specific limitations of one independent claim):**\n```\n{finder_input_payload['Claim Text']}\n```\n\n"
            f"**Priority Date:** {finder_input_payload['Priority Date']}\n\n"
            f"**Known Competitors (Prioritize these):** {json.dumps(finder_input_payload['Known Competitors']) if finder_input_payload['Known Competitors'] else 'None listed'}\n\n"
            f"**Assignee Companies (Do NOT list products from these):** {json.dumps(finder_input_payload['Assignee Companies']) if finder_input_payload['Assignee Companies'] else 'None listed'}"
        )

        finder_run = product_finder_agent.run(finder_query)

        if finder_run and finder_run.content:
            try:
                # Clean and extract JSON from the response
                json_str = extract_json_from_text(finder_run.content)
                parsed_list_p1 = json.loads(json_str)

                if isinstance(parsed_list_p1, list):
                    phase1_results = parsed_list_p1
                    logging.info(f"Phase 1 Success for {patent_number}: Found {len(phase1_results)} products.")
                else:
                    logging.error(f"Phase 1 Parsed JSON for {patent_number} is not a list")
                    phase1_results = []  # Return empty list instead of failing
            except json.JSONDecodeError as e:
                logging.error(f"Failed to decode JSON in Phase 1: {e}")
                logging.debug(f"Raw content that failed to parse: {repr(finder_run.content)}")
                # Return empty list instead of failing
                phase1_results = []
        else:
            logging.warning(f"Product finding (Phase 1) returned no content for {patent_number}")
    except Exception as e:
        logging.error(f"Error during Phase 1 for {patent_number}: {e}", exc_info=True)
        # Don't raise exception, just return empty results
        phase1_results = []

    return ProductsResponse(
        patent_number=patent_number,
        products=phase1_results
    )


@app.post("/claim_chart", response_model=ClaimChartResponse,
          summary="Create Claim Charts",
          description="Creates detailed claim charts for identified products against the patent's independent claims")
async def create_claim_charts(request: PatentRequest, products_data: Optional[ProductsResponse] = None):
    """Create claim charts for identified products (Phase 2)"""
    patent_number = request.patent_number

    # If products data wasn't provided, get it first
    if not products_data:
        novelty_data = await analyze_novelty(request)
        products_data = await find_products(request, novelty_data)

    phase1_results = products_data.products

    if not phase1_results:
        return ClaimChartResponse(
            patent_number=patent_number,
            claim_charts=[]
        )

    # Get patent data
    patent_data = cached_get_patent_full_details(patent_number)
    if patent_data.get("error"):
        raise HTTPException(status_code=404, detail=f"Failed to fetch patent details: {patent_data['error']}")

    independent_claims = patent_data.get("independent_claims", [])
    priority_date = patent_data.get("priority_date", "")

    # Initialize claim chart agent
    _, _, claim_chart_agent = init_agents()

    # Create claim charts
    phase2_results = []
    try:
        # Limit to 3 products maximum to prevent context overflow
        limited_products = phase1_results[:3]

        phase2_input_payload = {
            "Patent Details": {
                "Priority Date": priority_date,
                "Independent Claims": independent_claims[:1]  # Limit to first claim only to reduce complexity
            },
            "Identified Products List": limited_products
        }

        charting_query = (
            f"Analyze these products against the patent details. Return ONLY valid JSON as an array.\n"
            f"**Patent Details:**\n"
            f"  * Priority Date: {phase2_input_payload['Patent Details']['Priority Date']}\n"
            f"  * Independent Claims (focus on first claim):\n"
            f"```json\n{json.dumps(phase2_input_payload['Patent Details']['Independent Claims'], indent=2)}\n```\n\n"
            f"**Identified Products List ({len(phase2_input_payload['Identified Products List'])} total):\n"
            f"```json\n{json.dumps(phase2_input_payload['Identified Products List'], indent=2)}\n```\n\n"
            f"YOU MUST RETURN VALID JSON ONLY, starting with [ and ending with ]. No explanations before or after."
        )

        charting_run = claim_chart_agent.run(charting_query)

        if charting_run and charting_run.content:
            try:
                # Clean and extract JSON from the response
                json_str = extract_json_from_text(charting_run.content)

                # Handle edge case where response is empty JSON
                if json_str.strip() in ["[]", "{}"] or not json_str.strip():
                    logging.warning(f"Phase 2 returned empty JSON array for {patent_number}")
                    return ClaimChartResponse(
                        patent_number=patent_number,
                        claim_charts=[]
                    )

                parsed_list_p2 = json.loads(json_str)

                if isinstance(parsed_list_p2, list):
                    phase2_results = parsed_list_p2
                    logging.info(
                        f"Phase 2 Success for {patent_number}: Parsed analysis for {len(phase2_results)} products.")
                else:
                    logging.error(f"Phase 2 Parsed JSON for {patent_number} is not a list")
                    # If not a list, try to turn it into a one-item list
                    if isinstance(parsed_list_p2, dict):
                        phase2_results = [parsed_list_p2]
                    else:
                        phase2_results = []
            except json.JSONDecodeError as e:
                logging.error(f"Failed to decode JSON in Phase 2: {e}")
                logging.debug(f"Raw content that failed to parse: {repr(json_str)}")
                # Provide a fallback claim chart with error information
                phase2_results = []
                for product in limited_products:
                    phase2_results.append({
                        "company": product.get("company", "Unknown"),
                        "model": product.get("model", "Unknown"),
                        "claim_chart": [
                            {
                                "claim_element": "Error processing claim chart",
                                "product_element": "Could not analyze product elements",
                                "match_explanation": "JSON parsing error occurred"
                            }
                        ],
                        "infringement_risk_level": "Unknown",
                        "infringement_risk_explanation": "Error during claim chart generation"
                    })
        else:
            logging.error(f"Phase 2 agent returned no content for {patent_number}")
            # Provide a fallback empty response instead of failing
            phase2_results = []

    except Exception as e:
        logging.error(f"Error during Phase 2 for {patent_number}: {e}", exc_info=True)
        # Provide a fallback empty response instead of failing
        phase2_results = []

    return ClaimChartResponse(
        patent_number=patent_number,
        claim_charts=phase2_results
    )


@app.post("/excel_download",
          summary="Download Excel Analysis",
          description="Generates an Excel file with the analysis results")
async def download_excel(request: PatentRequest):
    """Generate and download Excel file with analysis results"""
    try:
        patent_number = request.patent_number

        # Run the full analysis to get the data
        try:
            # First try to run the full analysis endpoint
            full_analysis = await analyze_patent(request, BackgroundTasks())
            products_data = full_analysis.products
            claim_charts_data = full_analysis.claim_charts
        except Exception as e:
            logging.warning(f"Error in full analysis for excel download: {e}")
            # If that fails, run the individual steps
            novelty_data = await analyze_novelty(request)
            products_response = await find_products(request, novelty_data)
            claim_charts_response = await create_claim_charts(request, products_response)
            products_data = products_response.products
            claim_charts_data = claim_charts_response.claim_charts

        excel_buffer = create_excel_analysis(products_data, claim_charts_data)

        filename = f"Patent_Analysis_{request.patent_number}_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx"

        return StreamingResponse(
            excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logging.error(f"Error creating Excel file for download: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate Excel download: {str(e)}")


@app.post("/analyze_patent", response_model=FullAnalysisResponse,
          summary="Complete Patent Analysis",
          description="Analyzes a patent by running novelty analysis, product finding, and claim charting sequentially")
async def analyze_patent(request: PatentRequest, background_tasks: BackgroundTasks):
    """
    Performs a complete analysis of a patent:

    - Phase 0: Novelty Analysis - Identifies the core novelty of the patent
    - Phase 1: Product Finding - Identifies potentially infringing products
    - Phase 2: Claim Charting - Creates detailed claim charts for identified products

    Returns the combined results of all phases.

    Example patent numbers: US9253445B2, US7504937B2
    """
    patent_number = request.patent_number

    try:
        # Phase 0: Novelty Analysis
        novelty_response = await analyze_novelty(request)

        # Phase 1: Product Finding
        products_response = await find_products(request, novelty_response)

        # Phase 2: Claim Charting (only if products were found)
        claim_charts_response = await create_claim_charts(request, products_response)

        # Return combined results
        return FullAnalysisResponse(
            patent_number=patent_number,
            novelty_summary=novelty_response.novelty_summary,
            priority_date=novelty_response.priority_date,
            competitors=novelty_response.competitors,
            assignees=novelty_response.assignees,
            products=products_response.products,
            claim_charts=claim_charts_response.claim_charts
        )
    except Exception as e:
        logging.error(f"Error during full patent analysis for {patent_number}: {e}", exc_info=True)
        # Return partial results if possible
        return FullAnalysisResponse(
            patent_number=patent_number,
            novelty_summary=f"Error during analysis: {str(e)[:100]}...",
            priority_date=None,
            competitors=[],
            assignees=[],
            products=[],
            claim_charts=[]
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8010)