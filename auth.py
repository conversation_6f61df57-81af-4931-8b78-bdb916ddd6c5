import requests
import streamlit as st
import logging
import json
import sqlite3
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime, date, timedelta
import os
import hashlib
import uuid
from pathlib import Path
from dotenv import load_dotenv
import extra_streamlit_components as stx

load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

AUTH_API_URL = "https://api.auth.wissenresearch.com/auth/login"

# Database setup
DB_PATH = "user_credits.db"

# Initialize cookie manager directly without caching
cookie_manager = None


def init_cookie_manager():
    """Initialize the cookie manager without caching"""
    global cookie_manager
    if cookie_manager is None:
        cookie_manager = stx.CookieManager()
    return cookie_manager


def get_device_fingerprint(cookie_manager):
    """
    Generate a stable, unique fingerprint for the browser that persists across refreshes.
    It checks for a fingerprint in a dedicated cookie. If not found, it creates one.
    """
    FINGERPRINT_COOKIE_KEY = "device_fingerprint_id"

    # 1. First, check session_state for efficiency to avoid re-reading the cookie on every rerun.
    if 'device_fingerprint' in st.session_state and st.session_state.device_fingerprint:
        return st.session_state.device_fingerprint

    # 2. If not in session_state, try to get it from the persistent cookie.
    fingerprint = None
    try:
        fingerprint = cookie_manager.get(FINGERPRINT_COOKIE_KEY)
    except Exception as e:
        logger.warning(f"Could not read fingerprint cookie on initial try: {e}")

    # 3. If found in the cookie, use it and store it in session_state for the current session.
    if fingerprint:
        logger.info(f"Found existing device fingerprint in cookie: {fingerprint[:8]}...")
        st.session_state.device_fingerprint = fingerprint
        return fingerprint

    # 4. If not found anywhere, generate a new one, set the cookie, and store it in session_state.
    else:
        logger.info("No device fingerprint found. Generating a new one.")
        new_fingerprint = str(uuid.uuid4())
        st.session_state.device_fingerprint = new_fingerprint
        try:
            cookie_manager.set(
                FINGERPRINT_COOKIE_KEY,
                new_fingerprint,
                expires_at=datetime.now() + timedelta(days=365)  # Make it last for a year
            )
            logger.info(f"Set new device fingerprint cookie: {new_fingerprint[:8]}...")
        except Exception as e:
            logger.error(f"Failed to set fingerprint cookie: {e}")
        return new_fingerprint


def init_database():
    """Initialize SQLite database for user credits and sessions"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create users table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_credits (
        user_id INTEGER PRIMARY KEY,
        daily_credits_used INTEGER DEFAULT 0,
        total_credits_used INTEGER DEFAULT 0,
        last_request_date TEXT
    )
    ''')

    # Create user sessions table for tracking active sessions
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_token TEXT UNIQUE NOT NULL,
        device_fingerprint TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES user_credits (user_id)
    )
    ''')

    conn.commit()
    conn.close()
    logger.info("Database initialized")


def create_session_token(user_id: int, device_fingerprint: str, remember_me: bool = False) -> str:
    """Create a secure session token and store it in database"""
    session_token = str(uuid.uuid4())

    # Set expiry based on remember_me
    if remember_me:
        expires_at = datetime.now() + timedelta(days=7)
    else:
        expires_at = datetime.now() + timedelta(hours=24)  # 24 hour session

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Clean up expired sessions for this user
        cursor.execute(
            "DELETE FROM user_sessions WHERE user_id = ? AND expires_at < ?",
            (user_id, datetime.now())
        )

        # Insert new session
        cursor.execute('''
            INSERT INTO user_sessions
            (user_id, session_token, device_fingerprint, expires_at)
            VALUES (?, ?, ?, ?)
        ''', (user_id, session_token, device_fingerprint, expires_at))

        conn.commit()
        logger.info(f"Session token created for user {user_id} with fingerprint {device_fingerprint[:8]}...")
        return session_token

    except Exception as e:
        logger.error(f"Error creating session token: {str(e)}")
        conn.rollback()
        return None
    finally:
        conn.close()


def validate_session_token(session_token: str, device_fingerprint: str) -> Optional[int]:
    """Validate session token and return user_id if valid"""
    if not session_token or not device_fingerprint:
        return None

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Check if session exists, is active, not expired, and matches device fingerprint
        cursor.execute('''
            SELECT user_id FROM user_sessions
            WHERE session_token = ?
            AND device_fingerprint = ?
            AND is_active = 1
            AND expires_at > ?
        ''', (session_token, device_fingerprint, datetime.now()))

        result = cursor.fetchone()

        if result:
            user_id = result[0]

            # Update last_accessed timestamp
            cursor.execute('''
                UPDATE user_sessions
                SET last_accessed = ?
                WHERE session_token = ? AND device_fingerprint = ?
            ''', (datetime.now(), session_token, device_fingerprint))

            conn.commit()
            logger.info(f"Valid session found for user {user_id}")
            return user_id
        else:
            logger.info(f"Invalid session token or device fingerprint mismatch")
            return None

    except Exception as e:
        logger.error(f"Error validating session token: {str(e)}")
        return None
    finally:
        conn.close()


def invalidate_session(session_token: str):
    """Invalidate a specific session token"""
    if not session_token:
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "UPDATE user_sessions SET is_active = 0 WHERE session_token = ?",
            (session_token,)
        )
        conn.commit()
        logger.info("Session invalidated")
    except Exception as e:
        logger.error(f"Error invalidating session: {str(e)}")
    finally:
        conn.close()


def cleanup_expired_sessions():
    """Clean up expired sessions from database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "DELETE FROM user_sessions WHERE expires_at < ?",
            (datetime.now(),)
        )
        deleted_count = cursor.rowcount
        conn.commit()

        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} expired sessions")

    except Exception as e:
        logger.error(f"Error cleaning up expired sessions: {str(e)}")
    finally:
        conn.close()


def get_user_credits(user_id):
    """Get user credit information from database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if user exists in database
    cursor.execute("SELECT * FROM user_credits WHERE user_id = ?", (user_id,))
    user_data = cursor.fetchone()

    today = date.today().isoformat()

    if not user_data:
        # Create new user entry
        cursor.execute(
            "INSERT INTO user_credits (user_id, daily_credits_used, total_credits_used, last_request_date) VALUES (?, 0, 0, ?)",
            (user_id, today)
        )
        conn.commit()
        user_data = (user_id, 0, 0, today)

    # Reset daily credits if it's a new day
    last_request_date = user_data[3]
    daily_credits_used = user_data[1]
    total_credits_used = user_data[2]

    if last_request_date != today:
        cursor.execute(
            "UPDATE user_credits SET daily_credits_used = 0, last_request_date = ? WHERE user_id = ?",
            (today, user_id)
        )
        conn.commit()
        daily_credits_used = 0

    conn.close()

    return {
        "daily_credits_used": daily_credits_used,
        "total_credits_used": total_credits_used,
        "last_request_date": last_request_date
    }


def use_credit(user_id):
    """Use one credit for the user and update database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get current credit usage
    cursor.execute("SELECT daily_credits_used, total_credits_used FROM user_credits WHERE user_id = ?", (user_id,))
    result = cursor.fetchone()

    if not result:
        # This shouldn't happen if get_user_credits is called first, but just in case
        today = date.today().isoformat()
        cursor.execute(
            "INSERT INTO user_credits (user_id, daily_credits_used, total_credits_used, last_request_date) VALUES (?, 1, 1, ?)",
            (user_id, today)
        )
        daily_credits_used = 1
        total_credits_used = 1
    else:
        daily_credits_used = result[0] + 1
        total_credits_used = result[1] + 1

        cursor.execute(
            "UPDATE user_credits SET daily_credits_used = ?, total_credits_used = ? WHERE user_id = ?",
            (daily_credits_used, total_credits_used, user_id)
        )

    conn.commit()
    conn.close()

    logger.info(f"Credit used for user {user_id}. Daily: {daily_credits_used}, Total: {total_credits_used}")

    return {
        "daily_credits_used": daily_credits_used,
        "total_credits_used": total_credits_used
    }


def check_credits_available(user_id):
    """
    Check if user has credits available
    Returns (available, message) tuple
    """
    # Constants for credit limits
    DAILY_LIMIT = int(os.getenv("DAILY_LIMIT", 15))
    TOTAL_LIMIT = int(os.getenv("TOTAL_LIMIT", 50))

    # Get user credit data
    user_credits = get_user_credits(user_id)

    # Check daily limit
    daily_remaining = DAILY_LIMIT - user_credits["daily_credits_used"]
    if daily_remaining <= 0:
        return False, "⚠️ You've reached your daily limit of 15 credits. Please try again tomorrow."

    # Check total limit
    total_remaining = TOTAL_LIMIT - user_credits["total_credits_used"]
    if total_remaining <= 0:
        return False, "⚠️ You've reached your total limit of 50 credits. Please contact support for more credits."

    return True, ""


def logout():
    """Logout user and clear authentication data"""
    # Invalidate session token if it exists
    if hasattr(st.session_state, 'session_token') and st.session_state.session_token:
        invalidate_session(st.session_state.session_token)

    clear_authentication()
    st.success("Logged out successfully!")
    st.rerun()


def display_credits_sidebar(user_id):
    """Display credit information in the sidebar"""
    if st.button("Logout"):
        logout()

    # Constants for credit limits
    DAILY_LIMIT = 15
    TOTAL_LIMIT = 50

    # Get user credit data
    user_credits = get_user_credits(user_id)

    daily_used = user_credits["daily_credits_used"]
    total_used = user_credits["total_credits_used"]

    daily_remaining = DAILY_LIMIT - daily_used
    total_remaining = TOTAL_LIMIT - total_used

    st.sidebar.markdown("---")
    st.sidebar.subheader("Credits")

    # Display daily credits with appropriate color
    if daily_remaining <= 0:
        st.sidebar.error(f"Daily Credits: 0/{DAILY_LIMIT}")
        st.sidebar.warning("You've used all your daily credits. Credits will reset tomorrow.")
    elif daily_remaining <= 3:
        st.sidebar.warning(f"Daily Credits: {daily_remaining}/{DAILY_LIMIT}")
    else:
        st.sidebar.info(f"Daily Credits: {daily_remaining}/{DAILY_LIMIT}")

    # Display total credits with appropriate color
    if total_remaining <= 0:
        st.sidebar.error(f"Total Credits: 0/{TOTAL_LIMIT}")
        st.sidebar.warning("You've used all your total credits. Please contact support.")
    elif total_remaining <= 5:
        st.sidebar.warning(f"Total Credits: {total_remaining}/{TOTAL_LIMIT}")
    else:
        st.sidebar.success(f"Total Credits: {total_remaining}/{TOTAL_LIMIT}")

    return {
        "daily_remaining": daily_remaining,
        "total_remaining": total_remaining
    }


def login(email: str, password: str, totp_token: Optional[str] = None) -> Tuple[bool, str, Optional[Dict], bool]:
    """
    Authenticate user with email/password and optional TOTP token.
    Returns:
        (success, message, user_data, otp_required)
    """
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0"
    }

    payload = {"email": email, "password": password}
    if totp_token:
        payload["totp_token"] = totp_token

    try:
        response = requests.post(AUTH_API_URL, headers=headers, json=payload)
        logger.info(f"API response code: {response.status_code}")
        data = response.json()

        if response.status_code == 200:
            return True, "Login successful", data, False
        elif response.status_code == 400 and "TOTP" in data.get("error", ""):
            return False, data.get("error", "OTP required"), None, True
        else:
            return False, data.get("message", "Authentication failed"), None, False

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return False, f"Login error: {str(e)}", None, False


def clear_authentication():
    """Clear all authentication data"""
    global cookie_manager

    # Clear session state
    st.session_state.authenticated = False
    st.session_state.user_data = None
    st.session_state.user_id = None
    st.session_state.session_token = None
    st.session_state.otp_required = False
    st.session_state.email = ""
    st.session_state.password = ""
    # We don't clear the device_fingerprint from session_state, as it should persist for the tab's life

    # Clear the authentication cookie
    if cookie_manager:
        try:
            cookie_manager.delete("wissen_auth_session")
            logger.info("Authentication cookie cleared")
        except Exception as e:
            logger.error(f"Error clearing auth cookie: {str(e)}")


def set_persistent_cookie(user_data, session_token: str, remember_me: bool = False):
    """Set authentication cookie with session token"""
    global cookie_manager

    if not cookie_manager:
        logger.error("Cookie manager not initialized")
        return

    try:
        # Create cookie data with session token instead of user data
        auth_cookie_data = {
            "session_token": session_token,
            "user_id": user_data["id"],
            "created_at": datetime.now().isoformat(),
            "persistent": remember_me
        }

        if remember_me:
            # Set 7-day persistent cookie
            cookie_manager.set("wissen_auth_session", json.dumps(auth_cookie_data),
                               expires_at=datetime.now() + timedelta(days=7))
            logger.info(f"7-day persistent cookie set for user {user_data['id']}")
        else:
            # Set session cookie (expires when browser closes)
            cookie_manager.set("wissen_auth_session", json.dumps(auth_cookie_data))
            logger.info(f"Session cookie set for user {user_data['id']}")

    except Exception as e:
        logger.error(f"Error setting cookie: {str(e)}")


def check_authentication():
    """
    Check if user is authenticated and handle login flow.
    Returns True if authenticated, False otherwise.
    """
    # Initialize database on first run
    if not os.path.exists(DB_PATH):
        init_database()

    # Clean up expired sessions periodically
    cleanup_expired_sessions()

    # Initialize cookie manager
    global cookie_manager
    cookie_manager = init_cookie_manager()

    # Initialize session state if needed
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
        st.session_state.user_data = None
        st.session_state.user_id = None
        st.session_state.session_token = None
        st.session_state.otp_required = False
        st.session_state.email = ""
        st.session_state.password = ""
        st.session_state.remember_me = False
        st.session_state.device_fingerprint = None  # Initialize as None

    # Get or create the persistent device fingerprint
    device_fingerprint = get_device_fingerprint(cookie_manager)

    # Check for existing authentication in session state first
    if (st.session_state.authenticated and
            st.session_state.user_id and
            st.session_state.session_token):

        # Validate the session token
        user_id = validate_session_token(st.session_state.session_token, device_fingerprint)
        if user_id == st.session_state.user_id:
            return True
        else:
            # Session is invalid, clear authentication state
            logger.info("Session validation failed, clearing authentication")
            clear_authentication()

    # If not authenticated in session, check for auth cookie
    auth_cookie = None
    try:
        auth_cookie = cookie_manager.get("wissen_auth_session")
    except Exception as e:
        logger.error(f"Error reading cookie: {str(e)}")
        auth_cookie = None

    # If we have a cookie, validate and restore authentication
    if auth_cookie:
        try:
            auth_data = json.loads(auth_cookie)
            session_token = auth_data.get("session_token")

            if session_token:
                # Validate session token with the persistent device fingerprint
                user_id = validate_session_token(session_token, device_fingerprint)

                if user_id:
                    # Valid session, restore authentication into session_state
                    st.session_state.authenticated = True
                    st.session_state.user_id = user_id
                    st.session_state.session_token = session_token
                    st.session_state.user_data = {"id": user_id}

                    logger.info(f"User authenticated from cookie: {user_id}")
                    return True
                else:
                    # Invalid session token, clear the bad cookie
                    logger.info("Invalid session token from cookie, clearing cookie")
                    try:
                        cookie_manager.delete("wissen_auth_session")
                    except:
                        pass
            else:
                # No session token in cookie, clear it
                try:
                    cookie_manager.delete("wissen_auth_session")
                except:
                    pass

        except Exception as e:
            logger.error(f"Error parsing auth cookie: {str(e)}")
            # Clear malformed cookie
            try:
                cookie_manager.delete("wissen_auth_session")
            except:
                pass

    # If we reach here, user is not authenticated - show login form
    st.title("Wissen Research Login")

    # Step 1: Login Form
    if not st.session_state.otp_required:
        with st.form("login_form"):
            email = st.text_input("Email")
            password = st.text_input("Password", type="password")
            remember_me = st.checkbox("Remember me for 7 days")
            submit = st.form_submit_button("Login")

            if submit:
                if not email or not password:
                    st.error("Please enter both email and password")
                    return False

                success, message, user_data, otp_required = login(email, password)

                if success:
                    # Create session token
                    session_token = create_session_token(
                        user_data["id"],
                        device_fingerprint,
                        remember_me
                    )

                    if session_token:
                        st.session_state.authenticated = True
                        st.session_state.user_data = user_data
                        st.session_state.user_id = user_data["id"]
                        st.session_state.session_token = session_token

                        # Set cookie with session token
                        set_persistent_cookie(user_data, session_token, remember_me)

                        logger.info(f"User logged in with ID: {st.session_state.user_id}")
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Failed to create secure session. Please try again.")
                        return False

                elif otp_required:
                    st.session_state.otp_required = True
                    st.session_state.email = email
                    st.session_state.password = password
                    st.session_state.remember_me = remember_me
                    st.info("OTP required. Please check your email.")
                    st.rerun()
                else:
                    st.error(message)
                    return False

    # Step 2: OTP Form (shown only if required)
    elif st.session_state.otp_required:
        with st.form("otp_form"):
            st.subheader("Two-Factor Authentication")
            st.info("Please enter the OTP sent to your email")
            otp = st.text_input("OTP Code")
            otp_submit = st.form_submit_button("Verify")

            if otp_submit:
                if not otp:
                    st.error("Please enter the OTP code")
                    return False

                success, message, user_data, otp_required = login(
                    st.session_state.email,
                    st.session_state.password,
                    otp
                )

                if success:
                    # Create session token
                    session_token = create_session_token(
                        user_data["id"],
                        device_fingerprint,
                        st.session_state.remember_me
                    )

                    if session_token:
                        st.session_state.authenticated = True
                        st.session_state.user_data = user_data
                        st.session_state.user_id = user_data["id"]
                        st.session_state.session_token = session_token
                        st.session_state.otp_required = False

                        # Set cookie with session token
                        set_persistent_cookie(user_data, session_token, st.session_state.remember_me)

                        logger.info(f"User logged in with ID: {st.session_state.user_id}")
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Failed to create secure session. Please try again.")
                        return False
                else:
                    st.error(message)
                    return False

    return False